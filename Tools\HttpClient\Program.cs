using CommandLine;
using Ifs.Tools.HttpClient;
using System;
using System.Threading.Tasks;

namespace Ifs.Tools.HttpClient
{
    public class Options
    {
        [Option('u', "url", Required = true, HelpText = "URL to make GET request to")]
        public string Url { get; set; } = string.Empty;

        [Option('s', "server", Required = false, HelpText = "Server URL (e.g., https://server.com)")]
        public string? ServerUrl { get; set; }

        [Option('i', "systemid", Required = false, HelpText = "System ID for authentication")]
        public string? SystemId { get; set; }

        [Option('n', "username", Required = false, HelpText = "Username for TOKEN_DIRECT authentication")]
        public string? Username { get; set; }

        [Option('p', "password", Required = false, HelpText = "Password for TOKEN_DIRECT authentication")]
        public string? Password { get; set; }

        [Option('t', "token", Required = false, HelpText = "Direct access token (alternative to username/password)")]
        public string? AccessToken { get; set; }

        [Option('v', "verbose", Required = false, Default = false, HelpText = "Enable verbose output")]
        public bool Verbose { get; set; }
    }

    class Program
    {
        static async Task<int> Main(string[] args)
        {
            try
            {
                return await Parser.Default.ParseArguments<Options>(args)
                    .MapResult(
                        async (Options opts) => await RunAsync(opts),
                        errs => Task.FromResult(1));
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
                if (ex.InnerException != null)
                {
                    Console.WriteLine($"Inner Exception: {ex.InnerException.Message}");
                }
                return 1;
            }
        }

        static async Task<int> RunAsync(Options options)
        {
            try
            {
                if (options.Verbose)
                {
                    Console.WriteLine($"Making GET request to: {options.Url}");
                    Console.WriteLine($"Server: {options.ServerUrl ?? "Not specified"}");
                    Console.WriteLine($"System ID: {options.SystemId ?? "Not specified"}");
                    Console.WriteLine($"Username: {options.Username ?? "Not specified"}");
                    Console.WriteLine($"Using Token: {!string.IsNullOrEmpty(options.AccessToken)}");
                    Console.WriteLine();
                }

                var httpClient = new AuthenticatedHttpClient(options);
                var response = await httpClient.GetAsync(options.Url);

                if (options.Verbose)
                {
                    Console.WriteLine($"Response Status: {response.StatusCode}");
                    Console.WriteLine($"Response Headers:");
                    foreach (var header in response.Headers)
                    {
                        Console.WriteLine($"  {header.Key}: {string.Join(", ", header.Value)}");
                    }
                    Console.WriteLine();
                }

                var content = await response.Content.ReadAsStringAsync();
                Console.WriteLine("Response Content:");
                Console.WriteLine(content);

                return response.IsSuccessStatusCode ? 0 : 1;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error making request: {ex.Message}");
                if (options.Verbose && ex.InnerException != null)
                {
                    Console.WriteLine($"Inner Exception: {ex.InnerException.Message}");
                    Console.WriteLine($"Stack Trace: {ex.StackTrace}");
                }
                return 1;
            }
        }
    }
}
